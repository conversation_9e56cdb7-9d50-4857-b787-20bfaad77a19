import React from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'

const Navigation = ({ 
  currentPage = 'dashboard',
  showBackButton = false,
  backButtonText = '返回',
  backButtonPath = '/',
  customTitle = null
}) => {
  const navigate = useNavigate()
  const { user, profile, signOut } = useAuth()

  const handleSignOut = async () => {
    try {
      await signOut()
      navigate('/', { replace: true })
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  const handleLogoClick = () => {
    navigate('/')
  }

  const handleBackClick = () => {
    navigate(backButtonPath)
  }

  return (
    <nav className="bg-white/80 backdrop-blur-sm shadow-sm border-b border-white/20 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Left side - Logo/Back button */}
          <div className="flex items-center">
            {showBackButton ? (
              <button
                onClick={handleBackClick}
                className="inline-flex items-center gap-2 text-slate-600 hover:text-slate-900 font-medium transition-colors duration-200 group mr-4"
              >
                <svg className="w-4 h-4 transition-transform group-hover:-translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                {backButtonText}
              </button>
            ) : null}
            
            <div className="flex-shrink-0 flex items-center cursor-pointer" onClick={handleLogoClick}>
              <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">回</span>
              </div>
              <span className="ml-2 text-xl font-bold text-gray-900">回音角</span>
            </div>

            {customTitle && (
              <div className="ml-6">
                <h1 className="text-xl font-bold text-slate-900">{customTitle}</h1>
              </div>
            )}
          </div>

          {/* Right side - Navigation Links */}
          <div className="hidden md:flex items-center space-x-6">
            {/* Home button - only show if not on dashboard */}
            {currentPage !== 'dashboard' && (
              <button
                onClick={() => navigate('/')}
                className="text-gray-600 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors"
              >
                首页
              </button>
            )}

            {/* Dashboard button - only show if not on dashboard */}
            {currentPage !== 'dashboard' && (
              <button
                onClick={() => navigate('/dashboard')}
                className="text-gray-600 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors"
              >
                用户主页
              </button>
            )}

            {/* Create Post button - only show if not on create post page */}
            {currentPage !== 'create-post' && (
              <button
                onClick={() => navigate('/create-post')}
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                创作文章
              </button>
            )}

            {/* User info and points */}
            {profile && (
              <div className="flex items-center gap-2 px-3 py-1 bg-blue-50 rounded-full">
                <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
                <span className="text-sm text-blue-700 font-medium">
                  发文点数: {profile.posting_points || 0}
                </span>
              </div>
            )}

            {/* Sign out button */}
            <button
              onClick={handleSignOut}
              className="text-gray-500 hover:text-red-600 px-3 py-2 text-sm font-medium transition-colors"
            >
              退出登录
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={handleSignOut}
              className="text-gray-500 hover:text-red-600 px-3 py-2 text-sm font-medium transition-colors"
            >
              退出
            </button>
          </div>
        </div>
      </div>
    </nav>
  )
}

export default Navigation
