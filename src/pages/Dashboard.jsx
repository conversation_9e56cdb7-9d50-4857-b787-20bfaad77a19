import { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import { getPostsWithPagination, deletePost, getCommentsReceivedByUser, getCommentsMadeByUser, approveComment, getPostingPointsDetails, getSystemMessages, markMessageAsRead, getUnreadMessageCount, deleteUserAccount } from '../lib/supabaseClient'
import Navigation from '../components/Navigation'

const Dashboard = () => {
  const { user, profile, updateProfile, signOut } = useAuth()
  const navigate = useNavigate()
  const [isEditing, setIsEditing] = useState(false)
  const [editForm, setEditForm] = useState({
    username: profile?.username || '',
    bio: profile?.bio || '',
    avatar_url: profile?.avatar_url || ''
  })
  const [isUpdating, setIsUpdating] = useState(false)
  const [updateError, setUpdateError] = useState('')

  // Posts state
  const [userPosts, setUserPosts] = useState([])
  const [postsLoading, setPostsLoading] = useState(true)
  const [postsError, setPostsError] = useState('')
  const [postCount, setPostCount] = useState(0)

  // Comments state (received)
  const [receivedComments, setReceivedComments] = useState([])
  const [commentsLoading, setCommentsLoading] = useState(true)
  const [commentsError, setCommentsError] = useState('')
  const [commentCount, setCommentCount] = useState(0)

  // My comments state (made by user)
  const [myComments, setMyComments] = useState([])
  const [myCommentsLoading, setMyCommentsLoading] = useState(true)
  const [myCommentsError, setMyCommentsError] = useState('')
  const [myCommentCount, setMyCommentCount] = useState(0)
  const [approvedCommentCount, setApprovedCommentCount] = useState(0)
  const [pendingCommentCount, setPendingCommentCount] = useState(0)

  // Posting points state
  const [pointsDetails, setPointsDetails] = useState(null)
  const [pointsLoading, setPointsLoading] = useState(true)
  const [pointsError, setPointsError] = useState('')

  // System messages state
  const [systemMessages, setSystemMessages] = useState([])
  const [messagesLoading, setMessagesLoading] = useState(true)
  const [messagesError, setMessagesError] = useState('')
  const [unreadCount, setUnreadCount] = useState(0)

  // Settings state
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [deleteConfirmText, setDeleteConfirmText] = useState('')
  const [isDeleting, setIsDeleting] = useState(false)

  // Update form when profile changes
  useEffect(() => {
    if (profile) {
      setEditForm({
        username: profile.username || '',
        bio: profile.bio || '',
        avatar_url: profile.avatar_url || ''
      })
    }
  }, [profile])

  // Fetch user posts
  const fetchUserPosts = async () => {
    if (!user?.id) return

    try {
      setPostsLoading(true)
      setPostsError('')

      const { data, error, count } = await getPostsWithPagination({
        authorId: user.id,
        limit: 10,
        offset: 0
      })

      if (error) {
        throw new Error(error.message || '获取文章列表失败')
      }

      setUserPosts(data || [])
      setPostCount(count || 0)
    } catch (err) {
      console.error('Error fetching user posts:', err)
      setPostsError(err.message)
    } finally {
      setPostsLoading(false)
    }
  }

  // Fetch received comments
  const fetchReceivedComments = async () => {
    if (!user?.id) return

    try {
      setCommentsLoading(true)
      setCommentsError('')

      const { data, error, count } = await getCommentsReceivedByUser(user.id, {
        limit: 10,
        offset: 0
      })

      if (error) {
        throw new Error(error.message || '获取评论列表失败')
      }

      setReceivedComments(data || [])
      setCommentCount(count || 0)
    } catch (err) {
      console.error('Error fetching received comments:', err)
      setCommentsError(err.message)
    } finally {
      setCommentsLoading(false)
    }
  }

  // Fetch user's own comments
  const fetchMyComments = async () => {
    if (!user?.id) return

    try {
      setMyCommentsLoading(true)
      setMyCommentsError('')

      const { data, error, count } = await getCommentsMadeByUser(user.id, {
        limit: 20,
        offset: 0
      })

      if (error) {
        throw new Error(error.message || '获取我的评论列表失败')
      }

      const comments = data || []
      setMyComments(comments)
      setMyCommentCount(count || 0)

      // Calculate approved and pending counts
      const approved = comments.filter(comment => comment.is_author_approved).length
      const pending = comments.filter(comment => !comment.is_author_approved).length

      setApprovedCommentCount(approved)
      setPendingCommentCount(pending)
    } catch (err) {
      console.error('Error fetching my comments:', err)
      setMyCommentsError(err.message)
    } finally {
      setMyCommentsLoading(false)
    }
  }

  // Fetch posting points details
  const fetchPointsDetails = async () => {
    if (!user?.id) return

    try {
      setPointsLoading(true)
      setPointsError('')

      const { data, error } = await getPostingPointsDetails(user.id)

      if (error) {
        throw new Error(error.message || '获取发文点数详情失败')
      }

      setPointsDetails(data)
    } catch (err) {
      console.error('Error fetching points details:', err)
      setPointsError(err.message)
    } finally {
      setPointsLoading(false)
    }
  }

  // Fetch system messages
  const fetchSystemMessages = async () => {
    if (!user?.id) return

    try {
      setMessagesLoading(true)
      setMessagesError('')

      const { data, error } = await getSystemMessages(user.id, {
        limit: 20,
        includeRead: true
      })

      if (error) {
        throw new Error(error.message || '获取系统消息失败')
      }

      setSystemMessages(data || [])

      // Get unread count
      const { data: count, error: countError } = await getUnreadMessageCount(user.id)
      if (!countError) {
        setUnreadCount(count)
      }
    } catch (err) {
      console.error('Error fetching system messages:', err)
      setMessagesError(err.message)
    } finally {
      setMessagesLoading(false)
    }
  }

  // Mark message as read
  const handleMarkAsRead = async (messageId) => {
    try {
      const { error } = await markMessageAsRead(messageId, user.id)

      if (error) {
        throw new Error(error.message || '标记消息失败')
      }

      // Update local state
      setSystemMessages(prev =>
        prev.map(msg =>
          msg.id === messageId
            ? { ...msg, is_read: true }
            : msg
        )
      )

      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1))
    } catch (err) {
      console.error('Error marking message as read:', err)
      alert(err.message)
    }
  }

  // Handle logout
  const handleLogout = async () => {
    if (window.confirm('确定要退出登录吗？')) {
      try {
        await signOut()
        navigate('/', { replace: true })
      } catch (err) {
        console.error('Error signing out:', err)
        alert('退出登录失败，请重试')
      }
    }
  }

  // Handle account deletion
  const handleDeleteAccount = async () => {
    if (deleteConfirmText !== '删除我的账户') {
      alert('请输入正确的确认文本')
      return
    }

    setIsDeleting(true)

    try {
      const { error } = await deleteUserAccount(user.id)

      if (error) {
        throw new Error(error)
      }

      // Sign out and redirect
      await signOut()
      navigate('/', { replace: true })
      alert('账户已成功删除')
    } catch (err) {
      console.error('Error deleting account:', err)
      alert(err.message || '删除账户失败，请重试')
    } finally {
      setIsDeleting(false)
      setShowDeleteConfirm(false)
      setDeleteConfirmText('')
    }
  }

  // Load user posts and comments when user changes
  useEffect(() => {
    fetchUserPosts()
    fetchReceivedComments()
    fetchMyComments()
    fetchPointsDetails()
    fetchSystemMessages()
  }, [user?.id])

  // Handle post deletion
  const handleDeletePost = async (postId) => {
    if (!window.confirm('确定要删除这篇文章吗？此操作无法撤销。')) {
      return
    }

    try {
      const { error } = await deletePost(postId, user.id)

      if (error) {
        throw new Error(error.message || '删除文章失败')
      }

      // Remove the post from the list
      setUserPosts(prev => prev.filter(post => post.id !== postId))
      setPostCount(prev => prev - 1)

      // Show success message
      alert('文章删除成功')
    } catch (err) {
      console.error('Error deleting post:', err)
      alert(err.message)
    }
  }

  // Handle comment approval
  const handleApproveComment = async (commentId) => {
    try {
      const { error } = await approveComment(commentId)

      if (error) {
        throw new Error(error.message || '认可评论失败')
      }

      // Update the comment in the list
      setReceivedComments(prev =>
        prev.map(comment =>
          comment.id === commentId
            ? { ...comment, is_author_approved: true }
            : comment
        )
      )

      // Show success message
      alert('评论认可成功！评论者已获得 1 个发文点数奖励。')
    } catch (err) {
      console.error('Error approving comment:', err)
      alert(err.message)
    }
  }

  const handleEditToggle = () => {
    if (isEditing) {
      // Reset form to current profile data when canceling
      setEditForm({
        username: profile?.username || '',
        bio: profile?.bio || '',
        avatar_url: profile?.avatar_url || ''
      })
      setUpdateError('')
    }
    setIsEditing(!isEditing)
  }

  const handleFormChange = (e) => {
    const { name, value } = e.target
    setEditForm(prev => ({
      ...prev,
      [name]: value
    }))
    if (updateError) setUpdateError('')
  }

  const handleSaveProfile = async () => {
    setIsUpdating(true)
    setUpdateError('')

    try {
      // Basic validation
      if (!editForm.username.trim()) {
        setUpdateError('用户名不能为空')
        setIsUpdating(false)
        return
      }

      if (editForm.username.length < 3) {
        setUpdateError('用户名至少需要3个字符')
        setIsUpdating(false)
        return
      }

      if (editForm.bio && editForm.bio.length > 500) {
        setUpdateError('简介不能超过500个字符')
        setIsUpdating(false)
        return
      }

      const result = await updateProfile({
        username: editForm.username.trim(),
        bio: editForm.bio.trim() || null,
        avatar_url: editForm.avatar_url.trim() || null
      })

      if (result.success) {
        setIsEditing(false)
        // Success feedback could be added here
      } else {
        setUpdateError(result.error || '更新失败，请重试')
      }
    } catch (err) {
      setUpdateError('更新过程中发生错误，请重试')
    } finally {
      setIsUpdating(false)
    }
  }

  const handleSignOut = async () => {
    if (window.confirm('确定要退出登录吗？')) {
      await signOut()
      navigate('/', { replace: true })
    }
  }

  // Generate avatar initials
  const getAvatarInitials = () => {
    const name = profile?.username || user?.email || 'User'
    return name.charAt(0).toUpperCase()
  }

  // Format join date
  const formatJoinDate = (dateString) => {
    if (!dateString) return '未知'
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long'
    })
  }

  // Get content preview from Editor.js content
  const getContentPreview = (content) => {
    if (!content || !content.blocks || content.blocks.length === 0) {
      return '暂无内容预览'
    }

    // Find the first text block
    for (const block of content.blocks) {
      if (block.type === 'paragraph' && block.data?.text) {
        return block.data.text.length > 100
          ? block.data.text.substring(0, 100) + '...'
          : block.data.text
      }
      if (block.type === 'header' && block.data?.text) {
        return block.data.text.length > 100
          ? block.data.text.substring(0, 100) + '...'
          : block.data.text
      }
    }

    return '暂无内容预览'
  }

  // Get message type styling
  const getMessageTypeStyle = (type) => {
    const styles = {
      info: {
        bg: 'bg-blue-50',
        border: 'border-blue-200',
        icon: 'text-blue-500',
        text: 'text-blue-800',
        iconPath: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
      },
      success: {
        bg: 'bg-green-50',
        border: 'border-green-200',
        icon: 'text-green-500',
        text: 'text-green-800',
        iconPath: 'M5 13l4 4L19 7'
      },
      warning: {
        bg: 'bg-yellow-50',
        border: 'border-yellow-200',
        icon: 'text-yellow-500',
        text: 'text-yellow-800',
        iconPath: 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z'
      },
      error: {
        bg: 'bg-red-50',
        border: 'border-red-200',
        icon: 'text-red-500',
        text: 'text-red-800',
        iconPath: 'M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
      },
      announcement: {
        bg: 'bg-purple-50',
        border: 'border-purple-200',
        icon: 'text-purple-500',
        text: 'text-purple-800',
        iconPath: 'M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z'
      }
    }
    return styles[type] || styles.info
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Navigation Header */}
      <Navigation currentPage="dashboard" />

      {/* User Profile Section */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Title */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">用户主页</h1>
          <p className="text-gray-600">管理您的个人信息和创作内容</p>
        </div>

        {/* System Messages Section */}
        {(unreadCount > 0 || systemMessages.length > 0) && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <h2 className="text-xl font-bold text-gray-900">系统消息</h2>
                {unreadCount > 0 && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    {unreadCount} 条未读
                  </span>
                )}
              </div>
              <button
                onClick={fetchSystemMessages}
                disabled={messagesLoading}
                className="inline-flex items-center gap-2 px-3 py-1.5 text-sm bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 disabled:opacity-50 transition-colors duration-200"
              >
                <svg className={`w-4 h-4 ${messagesLoading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                刷新
              </button>
            </div>

            {messagesLoading ? (
              <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg shadow-slate-200/50 border border-white/20 p-6">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-3"></div>
                  <p className="text-gray-600 text-sm">加载系统消息中...</p>
                </div>
              </div>
            ) : messagesError ? (
              <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg shadow-slate-200/50 border border-white/20 p-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold text-red-800 mb-2">加载失败</h3>
                  <p className="text-red-600 text-sm mb-3">{messagesError}</p>
                  <button
                    onClick={fetchSystemMessages}
                    className="px-3 py-1.5 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                  >
                    重试
                  </button>
                </div>
              </div>
            ) : systemMessages.length > 0 ? (
              <div className="space-y-3">
                {systemMessages.slice(0, 3).map((message) => {
                  const style = getMessageTypeStyle(message.message_type)
                  return (
                    <div
                      key={message.id}
                      className={`${style.bg} ${style.border} border rounded-xl p-4 transition-all duration-200 ${
                        !message.is_read ? 'ring-2 ring-blue-200 ring-opacity-50' : ''
                      }`}
                    >
                      <div className="flex items-start gap-3">
                        <div className={`w-8 h-8 ${style.bg} rounded-lg flex items-center justify-center flex-shrink-0`}>
                          <svg className={`w-4 h-4 ${style.icon}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={style.iconPath} />
                          </svg>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between gap-3">
                            <div className="flex-1">
                              <h4 className={`font-semibold ${style.text} mb-1`}>
                                {message.title}
                                {!message.is_read && (
                                  <span className="ml-2 inline-block w-2 h-2 bg-red-500 rounded-full"></span>
                                )}
                              </h4>
                              <p className={`text-sm ${style.text} opacity-80 leading-relaxed`}>
                                {message.content}
                              </p>
                              <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                                <span>
                                  {new Date(message.created_at).toLocaleDateString('zh-CN', {
                                    month: 'long',
                                    day: 'numeric',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                  })}
                                </span>
                                {message.priority > 0 && (
                                  <span className="inline-flex items-center gap-1">
                                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                    </svg>
                                    优先级 {message.priority}
                                  </span>
                                )}
                              </div>
                            </div>
                            {!message.is_read && (
                              <button
                                onClick={() => handleMarkAsRead(message.id)}
                                className="text-xs text-blue-600 hover:text-blue-800 font-medium px-2 py-1 rounded hover:bg-blue-100 transition-colors"
                              >
                                标记已读
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}

                {systemMessages.length > 3 && (
                  <div className="text-center">
                    <p className="text-sm text-gray-500">
                      还有 {systemMessages.length - 3} 条消息...
                    </p>
                  </div>
                )}
              </div>
            ) : null}
          </div>
        )}

        {/* Profile Card */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl shadow-slate-200/50 overflow-hidden border border-white/20">
          {/* Profile Header */}
          <div className="relative">
            {/* Background Pattern */}
            <div className="h-32 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 relative overflow-hidden">
              <div className="absolute inset-0 bg-black/10"></div>
              <svg className="absolute bottom-0 left-0 w-full h-8" viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M0,120 Q300,80 600,100 T1200,80 L1200,120 Z" fill="rgba(255,255,255,0.1)"/>
              </svg>
            </div>

            {/* Avatar and Basic Info */}
            <div className="relative px-8 pb-6">
              <div className="flex flex-col sm:flex-row sm:items-end sm:space-x-6 -mt-16">
                {/* Avatar */}
                <div className="relative">
                  {profile?.avatar_url ? (
                    <img
                      src={profile.avatar_url}
                      alt="用户头像"
                      className="w-32 h-32 rounded-full border-4 border-white shadow-xl object-cover"
                    />
                  ) : (
                    <div className="w-32 h-32 rounded-full border-4 border-white shadow-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                      <span className="text-white text-4xl font-bold">
                        {getAvatarInitials()}
                      </span>
                    </div>
                  )}
                </div>

                {/* User Info */}
                <div className="mt-4 sm:mt-0 flex-1">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900">
                        {profile?.username || '用户'}
                      </h2>
                      <p className="text-gray-600 mt-1">
                        {formatJoinDate(profile?.created_at)} 加入
                      </p>
                    </div>

                    {/* Edit Button */}
                    <div className="mt-4 sm:mt-0">
                      {!isEditing ? (
                        <button
                          onClick={handleEditToggle}
                          className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200 shadow-lg hover:shadow-xl"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                          编辑资料
                        </button>
                      ) : (
                        <div className="flex gap-2">
                          <button
                            onClick={handleSaveProfile}
                            disabled={isUpdating}
                            className="inline-flex items-center gap-2 px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                          >
                            {isUpdating ? (
                              <>
                                <svg className="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                保存中...
                              </>
                            ) : (
                              <>
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                </svg>
                                保存
                              </>
                            )}
                          </button>
                          <button
                            onClick={handleEditToggle}
                            disabled={isUpdating}
                            className="px-4 py-2 bg-gray-500 text-white font-medium rounded-lg hover:bg-gray-600 disabled:opacity-50 transition-colors duration-200"
                          >
                            取消
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Profile Details */}
          <div className="px-8 pb-8">
            {/* Error Message */}
            {updateError && (
              <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl text-sm flex items-center gap-2">
                <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {updateError}
              </div>
            )}

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Basic Information */}
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                  基本信息
                </h3>

                {/* Username */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    用户名
                  </label>
                  {isEditing ? (
                    <input
                      type="text"
                      name="username"
                      value={editForm.username}
                      onChange={handleFormChange}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      placeholder="请输入用户名"
                      disabled={isUpdating}
                    />
                  ) : (
                    <div className="px-4 py-3 bg-gray-50 rounded-xl text-gray-900 font-medium">
                      {profile?.username || '未设置'}
                    </div>
                  )}
                </div>

                {/* Email (Read-only) */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    邮箱地址
                  </label>
                  <div className="px-4 py-3 bg-gray-50 rounded-xl text-gray-600">
                    {user?.email || '未设置'}
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    邮箱地址无法修改
                  </p>
                </div>

                {/* Avatar URL */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    头像链接
                  </label>
                  {isEditing ? (
                    <input
                      type="url"
                      name="avatar_url"
                      value={editForm.avatar_url}
                      onChange={handleFormChange}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      placeholder="请输入头像图片链接"
                      disabled={isUpdating}
                    />
                  ) : (
                    <div className="px-4 py-3 bg-gray-50 rounded-xl text-gray-600">
                      {profile?.avatar_url || '未设置'}
                    </div>
                  )}
                  <p className="mt-1 text-xs text-gray-500">
                    请输入有效的图片链接地址
                  </p>
                </div>
              </div>

              {/* Additional Information */}
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                  个人简介
                </h3>

                {/* Bio */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    简介
                  </label>
                  {isEditing ? (
                    <textarea
                      name="bio"
                      value={editForm.bio}
                      onChange={handleFormChange}
                      rows={4}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
                      placeholder="介绍一下自己吧..."
                      disabled={isUpdating}
                      maxLength={500}
                    />
                  ) : (
                    <div className="px-4 py-3 bg-gray-50 rounded-xl text-gray-700 min-h-[100px] whitespace-pre-wrap">
                      {profile?.bio || '这个人很懒，什么都没有留下...'}
                    </div>
                  )}
                  <p className="mt-1 text-xs text-gray-500">
                    {isEditing ? `${editForm.bio.length}/500 字符` : '最多500个字符'}
                  </p>
                </div>

                {/* Stats */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-3">
                    账户统计
                  </h4>
                  <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="bg-blue-50 rounded-xl p-4 text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {profile?.posting_points || 0}
                      </div>
                      <div className="text-sm text-blue-700 font-medium">
                        发文点数
                      </div>
                    </div>
                    <div className="bg-purple-50 rounded-xl p-4 text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {postsLoading ? '...' : postCount}
                      </div>
                      <div className="text-sm text-purple-700 font-medium">
                        发布文章
                      </div>
                    </div>
                    <div className="bg-green-50 rounded-xl p-4 text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {commentsLoading ? '...' : commentCount}
                      </div>
                      <div className="text-sm text-green-700 font-medium">
                        收到评论
                      </div>
                    </div>
                    <div className="bg-orange-50 rounded-xl p-4 text-center">
                      <div className="text-2xl font-bold text-orange-600">
                        {myCommentsLoading ? '...' : myCommentCount}
                      </div>
                      <div className="text-sm text-orange-700 font-medium">
                        发出评论
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg shadow-slate-200/50 p-6 border border-white/20">
            <div className="flex items-center gap-4 mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">创作文章</h3>
                <p className="text-gray-600 text-sm">分享您的想法和故事</p>
              </div>
            </div>
            <button
              onClick={() => navigate('/create-post')}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium py-3 rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              开始创作
            </button>
          </div>

          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg shadow-slate-200/50 p-6 border border-white/20">
            <div className="flex items-center gap-4 mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">浏览文章</h3>
                <p className="text-gray-600 text-sm">发现精彩的故事和内容</p>
              </div>
            </div>
            <button
              onClick={() => navigate('/')}
              className="w-full bg-gradient-to-r from-green-600 to-teal-600 text-white font-medium py-3 rounded-xl hover:from-green-700 hover:to-teal-700 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              去首页看看
            </button>
          </div>
        </div>

        {/* My Published Articles Section */}
        <div className="mt-12">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">我发布的文章</h2>
              <p className="text-gray-600 mt-1">管理您在平台上发布的所有文章</p>
            </div>
            <button
              onClick={fetchUserPosts}
              disabled={postsLoading}
              className="inline-flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 disabled:opacity-50 transition-colors duration-200"
            >
              <svg className={`w-4 h-4 ${postsLoading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              刷新
            </button>
          </div>

          {/* Posts List */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg shadow-slate-200/50 border border-white/20 overflow-hidden">
            {postsLoading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">加载文章列表中...</p>
              </div>
            ) : postsError ? (
              <div className="p-8 text-center">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-red-800 mb-2">加载失败</h3>
                <p className="text-red-600 mb-4">{postsError}</p>
                <button
                  onClick={fetchUserPosts}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  重试
                </button>
              </div>
            ) : userPosts.length === 0 ? (
              <div className="p-8 text-center">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-700 mb-2">还没有发布文章</h3>
                <p className="text-gray-500 mb-4">开始您的创作之旅，分享您的想法和故事</p>
                <button
                  onClick={() => navigate('/create-post')}
                  className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  创作第一篇文章
                </button>
              </div>
            ) : (
              <div className="divide-y divide-gray-100">
                {userPosts.map((post) => (
                  <div key={post.id} className="p-6 hover:bg-gray-50/50 transition-colors">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900 truncate">
                            {post.title}
                          </h3>
                          {post.is_anonymous && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                              匿名发布
                            </span>
                          )}
                        </div>

                        <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                          <span className="flex items-center gap-1">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {new Date(post.created_at).toLocaleDateString('zh-CN', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </span>
                          <span className="flex items-center gap-1">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            公开发布
                          </span>
                        </div>

                        {/* Content Preview */}
                        <div className="text-gray-600 text-sm line-clamp-2">
                          {getContentPreview(post.content)}
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center gap-2 ml-4">
                        <button
                          onClick={() => navigate(`/post/${post.id}`)}
                          className="inline-flex items-center gap-1 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                          查看
                        </button>
                        <button
                          onClick={() => handleDeletePost(post.id)}
                          className="inline-flex items-center gap-1 px-3 py-2 text-sm font-medium text-red-600 bg-red-50 rounded-lg hover:bg-red-100 transition-colors"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                          删除
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* My Received Comments Section */}
        <div className="mt-12">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">我收到的评论</h2>
              <p className="text-gray-600 mt-1">查看和管理其他用户对您文章的评论</p>
            </div>
            <button
              onClick={fetchReceivedComments}
              disabled={commentsLoading}
              className="inline-flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 disabled:opacity-50 transition-colors duration-200"
            >
              <svg className={`w-4 h-4 ${commentsLoading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              刷新
            </button>
          </div>

          {/* Comments List */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg shadow-slate-200/50 border border-white/20 overflow-hidden">
            {commentsLoading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">加载评论列表中...</p>
              </div>
            ) : commentsError ? (
              <div className="p-8 text-center">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-red-800 mb-2">加载失败</h3>
                <p className="text-red-600 mb-4">{commentsError}</p>
                <button
                  onClick={fetchReceivedComments}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  重试
                </button>
              </div>
            ) : receivedComments.length === 0 ? (
              <div className="p-8 text-center">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-700 mb-2">还没有收到评论</h3>
                <p className="text-gray-500 mb-4">当其他用户对您的文章发表评论时，会在这里显示</p>
                <button
                  onClick={() => navigate('/create-post')}
                  className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  发布更多文章
                </button>
              </div>
            ) : (
              <div className="divide-y divide-gray-100">
                {receivedComments.map((comment) => (
                  <div key={comment.id} className="p-6 hover:bg-gray-50/50 transition-colors">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        {/* Comment Header */}
                        <div className="flex items-center gap-3 mb-3">
                          {/* Commenter Avatar */}
                          <div className="flex-shrink-0">
                            {comment.profiles?.avatar_url ? (
                              <img
                                src={comment.profiles.avatar_url}
                                alt="评论者头像"
                                className="w-10 h-10 rounded-full object-cover"
                              />
                            ) : (
                              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                <span className="text-white text-sm font-bold">
                                  {comment.profiles?.username?.charAt(0)?.toUpperCase() || 'U'}
                                </span>
                              </div>
                            )}
                          </div>

                          {/* Commenter Info */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium text-gray-900">
                                {comment.profiles?.username || '匿名用户'}
                              </span>
                              {comment.is_author_approved && (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700">
                                  <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                  </svg>
                                  已认可
                                </span>
                              )}
                            </div>
                            <div className="text-sm text-gray-500">
                              评论于《{comment.posts?.title || '未知文章'}》 · {' '}
                              {new Date(comment.created_at).toLocaleDateString('zh-CN', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </div>
                          </div>
                        </div>

                        {/* Comment Content */}
                        <div className="ml-13 mb-4">
                          <div className="bg-gray-50 rounded-xl p-4 text-gray-700 leading-relaxed">
                            {comment.content}
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center gap-2 ml-4">
                        <button
                          onClick={() => navigate(`/post/${comment.posts?.id}`)}
                          className="inline-flex items-center gap-1 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                          查看文章
                        </button>
                        {!comment.is_author_approved && (
                          <button
                            onClick={() => handleApproveComment(comment.id)}
                            className="inline-flex items-center gap-1 px-3 py-2 text-sm font-medium text-green-600 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                            认可
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* My Comments Section */}
        <div className="mt-12">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">我发出的评论</h2>
              <p className="text-gray-600 mt-1">查看您对其他文章的评论状态</p>
            </div>
            <button
              onClick={fetchMyComments}
              disabled={myCommentsLoading}
              className="inline-flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 disabled:opacity-50 transition-colors duration-200"
            >
              <svg className={`w-4 h-4 ${myCommentsLoading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              刷新
            </button>
          </div>

          {/* Comment Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg shadow-slate-200/50 p-6 border border-white/20">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {myCommentsLoading ? '...' : approvedCommentCount}
                  </div>
                  <div className="text-sm text-green-700 font-medium">已回音评论</div>
                  <div className="text-xs text-gray-500">已获得作者认可</div>
                </div>
              </div>
            </div>

            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg shadow-slate-200/50 p-6 border border-white/20">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600">
                    {myCommentsLoading ? '...' : pendingCommentCount}
                  </div>
                  <div className="text-sm text-orange-700 font-medium">正待回音</div>
                  <div className="text-xs text-gray-500">等待作者认可</div>
                </div>
              </div>
            </div>
          </div>

          {/* Comments List */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg shadow-slate-200/50 border border-white/20 overflow-hidden">
            {myCommentsLoading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">加载评论列表中...</p>
              </div>
            ) : myCommentsError ? (
              <div className="p-8 text-center">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-red-800 mb-2">加载失败</h3>
                <p className="text-red-600 mb-4">{myCommentsError}</p>
                <button
                  onClick={fetchMyComments}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  重试
                </button>
              </div>
            ) : myComments.length === 0 ? (
              <div className="p-8 text-center">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-700 mb-2">还没有发出评论</h3>
                <p className="text-gray-500 mb-4">去阅读其他文章并发表您的看法吧</p>
                <button
                  onClick={() => navigate('/')}
                  className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  去首页看看
                </button>
              </div>
            ) : (
              <div className="divide-y divide-gray-100">
                {myComments.map((comment) => (
                  <div key={comment.id} className="p-6 hover:bg-gray-50/50 transition-colors">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        {/* Comment Header */}
                        <div className="flex items-center gap-3 mb-3">
                          {/* Post Author Avatar */}
                          <div className="flex-shrink-0">
                            {comment.posts?.profiles?.avatar_url ? (
                              <img
                                src={comment.posts.profiles.avatar_url}
                                alt="文章作者头像"
                                className="w-10 h-10 rounded-full object-cover"
                              />
                            ) : (
                              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                <span className="text-white text-sm font-bold">
                                  {comment.posts?.profiles?.username?.charAt(0)?.toUpperCase() || 'A'}
                                </span>
                              </div>
                            )}
                          </div>

                          {/* Comment Info */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="text-sm text-gray-600">
                                评论给 <span className="font-medium text-gray-900">{comment.posts?.profiles?.username || '匿名作者'}</span>
                              </span>
                              {comment.is_author_approved ? (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700">
                                  <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                  </svg>
                                  已回音
                                </span>
                              ) : (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-700">
                                  <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                  </svg>
                                  待回音
                                </span>
                              )}
                            </div>
                            <div className="text-sm text-gray-500">
                              《{comment.posts?.title || '未知文章'}》 · {' '}
                              {new Date(comment.created_at).toLocaleDateString('zh-CN', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </div>
                          </div>
                        </div>

                        {/* Comment Content */}
                        <div className="ml-13 mb-4">
                          <div className="bg-gray-50 rounded-xl p-4 text-gray-700 leading-relaxed">
                            {comment.content}
                          </div>
                        </div>
                      </div>

                      {/* Action Button */}
                      <div className="flex items-center gap-2 ml-4">
                        <button
                          onClick={() => navigate(`/post/${comment.posts?.id}`)}
                          className="inline-flex items-center gap-1 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                          查看文章
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* My Posting Points Section */}
        <div className="mt-12">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">我的发文点数</h2>
              <p className="text-gray-600 mt-1">查看您的发文点数详情和获取记录</p>
            </div>
            <button
              onClick={fetchPointsDetails}
              disabled={pointsLoading}
              className="inline-flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 disabled:opacity-50 transition-colors duration-200"
            >
              <svg className={`w-4 h-4 ${pointsLoading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              刷新
            </button>
          </div>

          {pointsLoading ? (
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg shadow-slate-200/50 border border-white/20 p-8">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">加载发文点数详情中...</p>
              </div>
            </div>
          ) : pointsError ? (
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg shadow-slate-200/50 border border-white/20 p-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-red-800 mb-2">加载失败</h3>
                <p className="text-red-600 mb-4">{pointsError}</p>
                <button
                  onClick={fetchPointsDetails}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  重试
                </button>
              </div>
            </div>
          ) : pointsDetails ? (
            <div className="space-y-6">
              {/* Current Points Overview */}
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg shadow-slate-200/50 border border-white/20 overflow-hidden">
                <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold mb-2">当前发文点数</h3>
                      <div className="text-4xl font-bold mb-2">{pointsDetails.currentPoints}</div>
                      <p className="text-blue-100">可发布 {pointsDetails.currentPoints} 篇文章</p>
                    </div>
                    <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center">
                      <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Points Breakdown */}
                <div className="p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">点数明细</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* Initial Points */}
                    <div className="bg-green-50 rounded-xl p-4 border border-green-200">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                          <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                          </svg>
                        </div>
                        <div>
                          <div className="text-2xl font-bold text-green-600">+{pointsDetails.initialPoints}</div>
                          <div className="text-sm text-green-700 font-medium">注册奖励</div>
                        </div>
                      </div>
                    </div>

                    {/* Earned Points */}
                    <div className="bg-blue-50 rounded-xl p-4 border border-blue-200">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                          <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        </div>
                        <div>
                          <div className="text-2xl font-bold text-blue-600">+{pointsDetails.pointsEarned}</div>
                          <div className="text-sm text-blue-700 font-medium">评论获得</div>
                        </div>
                      </div>
                    </div>

                    {/* Spent Points */}
                    <div className="bg-red-50 rounded-xl p-4 border border-red-200">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center">
                          <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                          </svg>
                        </div>
                        <div>
                          <div className="text-2xl font-bold text-red-600">-{pointsDetails.pointsSpent}</div>
                          <div className="text-sm text-red-700 font-medium">发文消耗</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* How to Earn Points */}
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg shadow-slate-200/50 border border-white/20 p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">如何获得发文点数</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center flex-shrink-0">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                    </div>
                    <div>
                      <h5 className="font-semibold text-gray-900 mb-2">发表优质评论</h5>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        对其他用户的文章发表有价值的评论，当作者认可您的评论时，您将获得 <span className="font-semibold text-green-600">1 个发文点数</span>。
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                    <div>
                      <h5 className="font-semibold text-gray-900 mb-2">新用户奖励</h5>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        注册成功后自动获得 <span className="font-semibold text-blue-600">5 个发文点数</span>，让您可以立即开始创作之旅。
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Points Usage */}
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg shadow-slate-200/50 border border-white/20 p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">点数使用说明</h4>
                <div className="bg-amber-50 border border-amber-200 rounded-xl p-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-amber-500 rounded-lg flex items-center justify-center flex-shrink-0">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h5 className="font-semibold text-amber-800 mb-2">发布文章消耗点数</h5>
                      <p className="text-amber-700 text-sm leading-relaxed">
                        每发布一篇文章需要消耗 <span className="font-semibold">1 个发文点数</span>。请确保您有足够的点数再进行创作。
                        通过积极参与社区讨论和发表优质评论来获得更多点数。
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Statistics Summary */}
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg shadow-slate-200/50 border border-white/20 p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">统计摘要</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                  <div className="bg-gray-50 rounded-xl p-4">
                    <div className="text-2xl font-bold text-gray-700">{pointsDetails.postsCount}</div>
                    <div className="text-sm text-gray-600 mt-1">已发布文章</div>
                  </div>
                  <div className="bg-gray-50 rounded-xl p-4">
                    <div className="text-2xl font-bold text-gray-700">{pointsDetails.approvedCommentsCount}</div>
                    <div className="text-sm text-gray-600 mt-1">获认可评论</div>
                  </div>
                  <div className="bg-gray-50 rounded-xl p-4">
                    <div className="text-2xl font-bold text-gray-700">{pointsDetails.initialPoints + pointsDetails.pointsEarned}</div>
                    <div className="text-sm text-gray-600 mt-1">总获得点数</div>
                  </div>
                  <div className="bg-gray-50 rounded-xl p-4">
                    <div className="text-2xl font-bold text-gray-700">
                      {new Date(pointsDetails.joinDate).toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' })}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">加入日期</div>
                  </div>
                </div>
              </div>
            </div>
          ) : null}
        </div>

        {/* Settings Section */}
        <div className="mt-12">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">设置</h2>
            <p className="text-gray-600">管理您的账户设置和偏好</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Logout Card */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg shadow-slate-200/50 border border-white/20 p-6">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">退出登录</h3>
                  <p className="text-gray-600 text-sm">安全退出您的账户</p>
                </div>
              </div>
              <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                退出登录后，您需要重新输入邮箱和密码才能访问您的账户。您的所有数据将被安全保存。
              </p>
              <button
                onClick={handleLogout}
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-medium py-3 rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                退出登录
              </button>
            </div>

            {/* Delete Account Card */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg shadow-slate-200/50 border border-white/20 p-6">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">注销账户</h3>
                  <p className="text-gray-600 text-sm">永久删除您的账户和所有数据</p>
                </div>
              </div>
              <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-4">
                <div className="flex items-start gap-3">
                  <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-red-800 text-sm mb-1">危险操作</h4>
                    <p className="text-red-700 text-xs leading-relaxed">
                      此操作将永久删除您的账户、个人资料、所有文章和评论。此操作无法撤销，请谨慎考虑。
                    </p>
                  </div>
                </div>
              </div>
              <button
                onClick={() => setShowDeleteConfirm(true)}
                className="w-full bg-gradient-to-r from-red-600 to-red-700 text-white font-medium py-3 rounded-xl hover:from-red-700 hover:to-red-800 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                注销账户
              </button>
            </div>
          </div>
        </div>

        {/* Delete Account Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-6">
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">确认注销账户</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  此操作将永久删除您的账户和所有相关数据，包括：
                </p>
              </div>

              <div className="bg-gray-50 rounded-xl p-4 mb-6">
                <ul className="text-sm text-gray-700 space-y-2">
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    个人资料和账户信息
                  </li>
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    所有发布的文章
                  </li>
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    所有发表的评论
                  </li>
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    发文点数和统计数据
                  </li>
                </ul>
              </div>

              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  请输入 <span className="font-bold text-red-600">"删除我的账户"</span> 来确认：
                </label>
                <input
                  type="text"
                  value={deleteConfirmText}
                  onChange={(e) => setDeleteConfirmText(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200"
                  placeholder="删除我的账户"
                  disabled={isDeleting}
                />
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => {
                    setShowDeleteConfirm(false)
                    setDeleteConfirmText('')
                  }}
                  disabled={isDeleting}
                  className="flex-1 px-4 py-3 bg-gray-100 text-gray-700 font-medium rounded-xl hover:bg-gray-200 disabled:opacity-50 transition-colors duration-200"
                >
                  取消
                </button>
                <button
                  onClick={handleDeleteAccount}
                  disabled={isDeleting || deleteConfirmText !== '删除我的账户'}
                  className="flex-1 px-4 py-3 bg-red-600 text-white font-medium rounded-xl hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center justify-center gap-2"
                >
                  {isDeleting ? (
                    <>
                      <svg className="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      删除中...
                    </>
                  ) : (
                    '确认删除'
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

    </div>
  )
}

export default Dashboard
