import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { getPost, getComments, createComment } from '../lib/supabaseClient'
import Comment from '../components/Comment'
import Navigation from '../components/Navigation'

const PostDetail = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const { user } = useAuth()
  
  const [post, setPost] = useState(null)
  const [comments, setComments] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [newComment, setNewComment] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Load post and comments
  useEffect(() => {
    const loadPostData = async () => {
      try {
        setLoading(true)
        
        // Load post
        const { data: postData, error: postError } = await getPost(id)
        if (postError) {
          throw new Error(postError.message || '无法加载文章')
        }
        setPost(postData)

        // Load comments
        const { data: commentsData, error: commentsError } = await getComments(id)
        if (commentsError) {
          throw new Error(commentsError.message || '无法加载评论')
        }
        setComments(commentsData || [])

      } catch (err) {
        console.error('Error loading post:', err)
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    if (id) {
      loadPostData()
    }
  }, [id])

  // Handle new comment submission
  const handleSubmitComment = async (e) => {
    e.preventDefault()
    
    if (!user) {
      alert('请先登录后再评论')
      return
    }

    if (!newComment.trim()) {
      alert('请输入评论内容')
      return
    }

    try {
      setIsSubmitting(true)
      
      const commentData = {
        post_id: parseInt(id),
        commenter_id: user.id,
        content: newComment.trim()
      }

      const { data, error } = await createComment(commentData)
      if (error) {
        throw new Error(error.message || '发布评论失败')
      }

      // Add new comment to the list
      setComments(prev => [...prev, data])
      setNewComment('')
      
    } catch (err) {
      console.error('Error creating comment:', err)
      alert(err.message)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle comment approval
  const handleCommentApproved = (commentId) => {
    setComments(prev => 
      prev.map(comment => 
        comment.id === commentId 
          ? { ...comment, is_author_approved: true }
          : comment
      )
    )
  }

  // Simple content renderer
  const renderContent = (content) => {
    if (!content || !content.blocks) {
      return <p className="text-slate-600">暂无内容</p>
    }

    return content.blocks.map((block, index) => {
      switch (block.type) {
        case 'header':
          const level = block.data.level || 2
          if (level === 1) {
            return <h1 key={index} className="text-4xl font-bold mb-8 text-slate-900">{block.data.text}</h1>
          } else if (level === 2) {
            return <h2 key={index} className="text-3xl font-bold mb-6 text-slate-900">{block.data.text}</h2>
          } else if (level === 3) {
            return <h3 key={index} className="text-2xl font-semibold mb-5 text-slate-800">{block.data.text}</h3>
          } else {
            return <h4 key={index} className="text-xl font-semibold mb-4 text-slate-800">{block.data.text}</h4>
          }
        case 'paragraph':
          return <p key={index} className="mb-6 text-slate-700 leading-relaxed text-lg">{block.data.text}</p>
        case 'list':
          const ListTag = block.data.style === 'ordered' ? 'ol' : 'ul'
          const listClass = block.data.style === 'ordered' ? 'list-decimal' : 'list-disc'
          return (
            <ListTag key={index} className={`mb-6 ${listClass} list-inside space-y-2 text-lg`}>
              {block.data.items.map((item, itemIndex) => (
                <li key={itemIndex} className="text-slate-700 leading-relaxed pl-2">{item}</li>
              ))}
            </ListTag>
          )
        case 'quote':
          return (
            <blockquote key={index} className="relative border-l-4 border-blue-500 pl-6 pr-4 py-4 mb-8 bg-slate-50 rounded-r-xl">
              <p className="text-slate-700 italic text-lg leading-relaxed font-medium">{block.data.text}</p>
              {block.data.caption && (
                <cite className="text-sm text-slate-500 mt-3 block font-normal not-italic">— {block.data.caption}</cite>
              )}
            </blockquote>
          )
        case 'code':
          return (
            <pre key={index} className="bg-slate-900 p-6 rounded-xl mb-8 overflow-x-auto shadow-lg">
              <code className="text-sm text-green-400 font-mono leading-relaxed">{block.data.code}</code>
            </pre>
          )
        default:
          return (
            <div key={index} className="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-xl">
              <p className="text-sm text-amber-700 font-medium">未知内容类型: {block.type}</p>
            </div>
          )
      }
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载文章...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-2">加载失败</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => navigate('/')}
            className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
          >
            返回首页
          </button>
        </div>
      </div>
    )
  }

  if (!post) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">文章不存在</h2>
          <p className="text-gray-600 mb-4">您访问的文章可能已被删除或不存在</p>
          <button
            onClick={() => navigate('/')}
            className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
          >
            返回首页
          </button>
        </div>
      </div>
    )
  }

  const isAuthor = user && user.id === post.author_id

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      {/* Navigation Header */}
      <Navigation
        currentPage="post-detail"
        showBackButton={true}
        backButtonText="返回首页"
        backButtonPath="/"
      />

      {/* Hero Section */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 to-purple-600/5"></div>
        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-12 pb-8">
          <div className="text-center">
            <h1 className="text-4xl sm:text-5xl font-bold text-slate-900 mb-6 leading-tight">
              {post.title}
            </h1>
            
            {/* Author Info */}
            <div className="flex items-center justify-center gap-4 mb-8">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-lg">
                  {post.is_anonymous ? '匿' : (post.profiles?.username?.charAt(0)?.toUpperCase() || '?')}
                </div>
                <div className="text-left">
                  <div className="font-medium text-slate-900">
                    {post.is_anonymous ? '匿名用户' : post.profiles?.username || '未知用户'}
                  </div>
                  <div className="text-sm text-slate-500">
                    {new Date(post.created_at).toLocaleDateString('zh-CN', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </div>
                </div>
              </div>
              
              {isAuthor && (
                <div className="inline-flex items-center gap-2 px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                  您的文章
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
        <article className="bg-white rounded-2xl shadow-xl shadow-slate-200/50 overflow-hidden">
          <div className="p-8 sm:p-12">
            <div className="prose prose-lg prose-slate max-w-none">
              <div className="text-slate-700 leading-relaxed">
                {renderContent(post.content)}
              </div>
            </div>
          </div>
        </article>

        {/* Comments Section */}
        <div className="mt-12">
          <div className="bg-white rounded-2xl shadow-xl shadow-slate-200/50 overflow-hidden">
            <div className="p-8 sm:p-12">
              <div className="flex items-center gap-3 mb-8">
                <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-slate-900">
                  讨论区 <span className="text-lg font-normal text-slate-500">({comments.length})</span>
                </h2>
              </div>

              {/* Comment Form */}
              {user ? (
                <form onSubmit={handleSubmitComment} className="mb-10">
                  <div className="mb-6">
                    <label htmlFor="comment" className="block text-sm font-semibold text-slate-700 mb-3">
                      参与讨论
                    </label>
                    <div className="relative">
                      <textarea
                        id="comment"
                        rows={4}
                        value={newComment}
                        onChange={(e) => setNewComment(e.target.value)}
                        placeholder="分享您的想法和见解..."
                        className="w-full px-4 py-3 border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none bg-slate-50/50 hover:bg-white"
                        disabled={isSubmitting}
                      />
                    </div>
                  </div>
                  <div className="flex justify-end">
                    <button
                      type="submit"
                      disabled={isSubmitting || !newComment.trim()}
                      className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-xl hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
                    >
                      {isSubmitting ? (
                        <>
                          <svg className="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          发布中...
                        </>
                      ) : (
                        <>
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                          </svg>
                          发布评论
                        </>
                      )}
                    </button>
                  </div>
                </form>
              ) : (
                <div className="mb-10 p-6 bg-gradient-to-r from-slate-50 to-slate-100 rounded-xl border border-slate-200 text-center">
                  <div className="w-12 h-12 bg-slate-200 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-6 h-6 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <p className="text-slate-600 mb-4 font-medium">登录后参与讨论</p>
                  <button
                    onClick={() => navigate('/login')}
                    className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                    立即登录
                  </button>
                </div>
              )}

              {/* Comments List */}
              <div className="space-y-6">
                {comments.length > 0 ? (
                  comments.map((comment) => (
                    <Comment
                      key={comment.id}
                      comment={comment}
                      isPostAuthor={isAuthor}
                      onApproved={() => handleCommentApproved(comment.id)}
                    />
                  ))
                ) : (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                    </div>
                    <p className="text-slate-500 text-lg font-medium mb-2">还没有评论</p>
                    <p className="text-slate-400">成为第一个分享想法的人吧！</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PostDetail
