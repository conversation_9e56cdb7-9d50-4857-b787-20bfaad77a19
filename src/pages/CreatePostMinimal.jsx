import React, { useState } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import Editor from '../components/Editor'
import Navigation from '../components/Navigation'

const CreatePostMinimal = () => {
  const [title, setTitle] = useState('')
  const { user, profile } = useAuth()
  const navigate = useNavigate()



  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">请先登录</h2>
          <p className="text-gray-600">您需要登录后才能创作文章</p>
          <a href="/login" className="mt-4 inline-block px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700">
            去登录
          </a>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation Header */}
      <Navigation
        currentPage="create-post"
        showBackButton={true}
        backButtonText="返回主页"
        backButtonPath="/"
        customTitle="创作新文章 (最小版本)"
      />

      <div className="max-w-4xl mx-auto p-8">
        
        <div className="bg-white rounded-lg shadow p-6">
          {/* Title Input */}
          <div className="mb-6">
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
              文章标题
            </label>
            <input
              type="text"
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="请输入文章标题..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>

          {/* Editor */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              文章内容
            </label>
            <Editor
              onChange={() => {}}
              placeholder="开始写作..."
            />
          </div>

          {/* User Info */}
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <h4 className="text-sm font-medium text-blue-800 mb-2">用户信息</h4>
            <div className="text-xs text-blue-700 space-y-1">
              <p><strong>用户ID:</strong> {user?.id || 'N/A'}</p>
              <p><strong>用户邮箱:</strong> {user?.email || 'N/A'}</p>
              <p><strong>用户名:</strong> {profile?.username || 'N/A'}</p>
              <p><strong>发文点数:</strong> {profile?.posting_points || 0}</p>
            </div>
          </div>

          {/* Actions */}
          <div className="mt-6 flex space-x-4">
            <button
              onClick={() => console.log('Save clicked', { title })}
              className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
            >
              保存 (测试)
            </button>
            <a
              href="/"
              className="inline-block px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              返回首页
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CreatePostMinimal
